
<template>
	<view class="page">
		<view class="" v-if="addressArr.length>0">
			<view class="address_item" v-for="(item,index) in addressArr" :key="index" @click="choose(item)">
				<view class="head">
					<view class="mr" v-if="item.status == 1">默认</view>
					<text>{{item.address}}</text>
					<span></span>
				</view>
				<view class="body">
					<view class="left">{{item.addressInfo}}</view>
					<view class="right" @click.stop="goUrl(item)">
						<image src="../static/images/9369.png" mode=""></image>
					</view>
				</view>
				<view class="foot">
					<view class="box">{{item.userName}}</view>
					<view class="box">{{item.sex == 1?'（先生）':'（女士）'}}</view>
					<view class="box">{{item.mobile}}</view>
				</view>
			</view>
		</view>
		<view v-if="addressArr.length < total" class="load-more" @click="loadMore">加载更多</view>
		<view class="footer">
			<view class="btn" @tap="goUrlAdd('../user/add_address')">新增地址</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			addressArr: [],
			page: 1,
			limit: 10,
			total: 0, // Initialize total count for pagination
			loading: false // Prevent multiple simultaneous requests
		}
	},
	onPullDownRefresh() {
		console.log('refresh');
		// Reset to first page on pull-down refresh
		this.page = 1;
		this.addressArr = [];
		this.getAddressList();
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	onReachBottom() {
		// Triggered when user scrolls to the bottom
		if (this.addressArr.length < this.total && !this.loading) {
			this.page += 1;
			this.getAddressList();
		}
	},
	methods: {
		goUrl(item) {
			console.log(item)
			console.log(item.id)
			uni.setStorageSync("editAdress", item);
			const e = `../user/edit_address?id=${item.id}`
			uni.navigateTo({
				url: e
			})
		},
		goUrlAdd(e) {
			uni.navigateTo({
				url: e
			})
		},
		choose(e) {
			let pages = getCurrentPages() // Get stack instance
			// Check if there are at least two pages in the stack before accessing the previous page
			if (pages.length >= 2) {
				let prevPage = pages[pages.length - 2] // Get data from the previous page, including the page itself
				if (prevPage && prevPage.route === 'user/price_parity') {
					uni.$emit('chooseAddress', e)
					uni.navigateBack()
				}
			}
			// If not coming from 'confirm_order' or no previous page, do nothing (return implicitly)
		},
		async getAddressList() {
			if (this.loading) return; // Prevent multiple requests
			this.loading = true;
			try {
				let res = await this.$api.mine.addressList({
					pageNum: this.page,
					pageSize: this.limit
				});
				console.log(res);
				this.total = res.data.totalCount; // Update total count
				// Append new items to the list or reset if page 1
				const newList = res.data.list.sort((a, b) => b.status - a.status);
				this.addressArr = this.page === 1 ? newList : [...this.addressArr, ...newList];
			} finally {
				this.loading = false;
			}
		},
		loadMore() {
			// Manual load more button
			if (this.addressArr.length < this.total && !this.loading) {
				this.page += 1;
				this.getAddressList();
			}
		}
	},
	onShow() {
		this.page = 1; // Reset page on show
		this.addressArr = []; // Clear list
		this.getAddressList();
	}
}
</script>

<style scoped lang="scss">
.page {
	height: 100vh;
	padding: 40rpx 0;
	overflow: auto;
	padding-bottom: 200rpx;
	background-color: #f8f8f8;

	.address_item {
		background-color: #fff;
		padding: 18rpx 30rpx;
		margin-bottom: 20rpx;

		.head {
			display: flex;
			align-items: center;

			.mr {
				width: 72rpx;
				height: 38rpx;
				background: #CCE0FF;
				border-radius: 4rpx 4rpx 4rpx 4rpx;
				border: 2rpx solid #2E80FE;
				font-size: 20rpx;
				font-weight: 400;
				color: #2E80FE;
				line-height: 38rpx;
				text-align: center;
			}

			text {
				margin-left: 36rpx;
				font-size: 28rpx;
				font-weight: 600;
				color: #333333;
			}

			span {
				margin-left: 20rpx;
				font-size: 28rpx;
				font-weight: 400;
				color: #333333;
			}
		}

		.body {
			margin-top: 18rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.left {
				font-size: 24rpx;
				font-weight: 500;
				color: #333333;
				max-width: 500rpx;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}

			.right {
				image {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}

		.foot {
			margin-top: 20rpx;
			display: flex;
			align-items: center;
			font-size: 24rpx;
			font-weight: 500;
			color: #999999;

			.box {
				margin-right: 15rpx;
			}
		}
	}

	.load-more {
		text-align: center;
		padding: 20rpx;
		font-size: 28rpx;
		color: #2e80fe;
		cursor: pointer;
	}

	.footer {
		padding: 52rpx 30rpx;
		position: fixed;
		bottom: 0;
		background-color: #fff;

		.btn {
			width: 690rpx;
			height: 98rpx;
			background: #2E80FE;
			border-radius: 50rpx 50rpx 50rpx 50rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #FFFFFF;
			line-height: 98rpx;
			text-align: center;
		}
	}
}
</style>
```