<template>
	<view class="login-container">
		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<u-icon name="arrow-left" color="#333" size="24"></u-icon>
			</view>
			<view class="title">{{ currentMode === 'login' ? '登录' : currentMode === 'register' ? '注册' : '忘记密码' }}</view>
		</view>

		<!-- Logo Section -->
		<view class="logo-section">
			<image class="logo" src="/static/images/logo-index.jpg" mode="aspectFit"></image>
			<view class="app-name">今师傅</view>
			<view class="welcome-text">{{ getWelcomeText() }}</view>
		</view>

		<!-- Login Form -->
		<view class="form-container" v-if="currentMode === 'login'">
			<!-- Tab Switcher -->
			<view class="tab-switcher">
				<view class="tab-item" :class="{ active: loginType === 'password' }" @click="switchLoginType('password')">
					密码登录
				</view>
				<view class="tab-item" :class="{ active: loginType === 'sms' }" @click="switchLoginType('sms')">
					验证码登录
				</view>
			</view>

			<!-- Password Login -->
			<view v-if="loginType === 'password'">
				<view class="input-group">
					<view class="input-item">
						<u-icon name="phone" color="#999" size="20"></u-icon>
						<input class="input-field" type="number" placeholder="请输入手机号" v-model="loginForm.phone" maxlength="11" />
					</view>
					<view class="input-item">
						<u-icon name="lock" color="#999" size="20"></u-icon>
						<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码" v-model="loginForm.password" />
						<view class="eye-icon" @click="togglePassword">
							<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
						</view>
					</view>
				</view>
				<view class="forgot-password" @click="switchMode('forgot')">忘记密码？</view>
			</view>

			<!-- SMS Login -->
			<view v-if="loginType === 'sms'">
				<view class="input-group">
					<view class="input-item">
						<u-icon name="phone" color="#999" size="20"></u-icon>
						<input class="input-field" type="number" placeholder="请输入手机号" v-model="smsForm.phone" maxlength="11" />
					</view>
					<view class="input-item">
						<u-icon name="checkmark-circle" color="#999" size="20"></u-icon>
						<input class="input-field" type="number" placeholder="请输入验证码" v-model="smsForm.code" maxlength="6" />
						<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
							{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- Register Form -->
		<view class="form-container" v-if="currentMode === 'register'">
			<view class="input-group">
				<view class="input-item">
					<u-icon name="phone" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入手机号" v-model="registerForm.phone" maxlength="11" />
				</view>
				<view class="input-item">
					<u-icon name="lock" color="#999" size="20"></u-icon>
					<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请设置密码" v-model="registerForm.password" />
					<view class="eye-icon" @click="togglePassword">
						<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
					</view>
				</view>
				<view class="input-item">
					<u-icon name="checkmark-circle" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入验证码" v-model="registerForm.shortCode" maxlength="6" />
					<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
						{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
					</view>
				</view>
				<view class="input-item">
					<u-icon name="gift" color="#999" size="20"></u-icon>
					<input class="input-field" type="text" placeholder="邀请码（选填）" v-model="registerForm.pidInviteCode" />
				</view>
			</view>
		</view>

		<!-- Forgot Password Form -->
		<view class="form-container" v-if="currentMode === 'forgot'">
			<view class="input-group">
				<view class="input-item">
					<u-icon name="phone" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入手机号" v-model="forgotForm.phone" maxlength="11" />
				</view>
				<view class="input-item">
					<u-icon name="lock" color="#999" size="20"></u-icon>
					<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请输入新密码" v-model="forgotForm.newPassword" />
					<view class="eye-icon" @click="togglePassword">
						<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
					</view>
				</view>
				<view class="input-item">
					<u-icon name="lock" color="#999" size="20"></u-icon>
					<input class="input-field" :type="showConfirmPassword ? 'text' : 'password'" placeholder="请确认新密码" v-model="forgotForm.confirmPassword" />
					<view class="eye-icon" @click="toggleConfirmPassword">
						<u-icon :name="showConfirmPassword ? 'eye' : 'eye-off'" color="#999" size="20"></u-icon>
					</view>
				</view>
				<view class="input-item">
					<u-icon name="checkmark-circle" color="#999" size="20"></u-icon>
					<input class="input-field" type="number" placeholder="请输入验证码" v-model="forgotForm.shortCode" maxlength="6" />
					<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
						{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
					</view>
				</view>
			</view>
		</view>

		<!-- Agreement Section -->
		<view class="agreement-section">
			<view class="checkbox-container" @click="toggleAgreement">
				<view class="checkbox" :class="{ checked: agreedToTerms }">
					<u-icon v-if="agreedToTerms" name="checkmark" color="#fff" size="14"></u-icon>
				</view>
				<view class="agreement-text">
					我已阅读并同意
					<text class="link" @click.stop="navigateToAgreement('service')">《今师傅服务协议》</text>
					和
					<text class="link" @click.stop="navigateToAgreement('privacy')">《隐私政策》</text>
				</view>
			</view>
		</view>

		<!-- Action Button -->
		<view class="action-button" :class="{ disabled: !canSubmit }" @click="handleSubmit">
			{{ isLoading ? '处理中...' : getButtonText() }}
		</view>
		
		<!-- WeChat Login Button -->
		<view class="wechat-login-button" @click="handleWechatLogin" v-if="currentMode === 'login'">
			<image class="wechat-icon" src="/static/images/wechat-icon.png" mode="aspectFit"></image>
			<text>微信登录</text>
		</view>

		<!-- Debug Buttons (temporary) - 隐藏调试按钮 -->
		<!-- <view class="debug-button" @click="testWechatConfig" v-if="currentMode === 'login'">
			<text>测试微信配置</text>
		</view>
		<view class="debug-button simple" @click="simpleWechatLogin" v-if="currentMode === 'login'">
			<text>简化版微信登录</text>
		</view> -->

		<!-- Switch Mode Links -->
		<view class="switch-links">
			<view v-if="currentMode === 'login'" class="link-text" @click="switchMode('register')">
				还没有账号？立即注册
			</view>
			<view v-if="currentMode === 'register'" class="link-text" @click="switchMode('login')">
				已有账号？立即登录
			</view>
			<view v-if="currentMode === 'forgot'" class="link-text" @click="switchMode('login')">
				返回登录
			</view>
		</view>
	</view>
</template>

<script>
	import { mapMutations } from 'vuex';
	import { md5 } from '@/utils/md5.js';
	import { extractTokenFromHeaders } from '@/utils/cookieParser.js';

	export default {
		data() {
			return {
				currentMode: 'login', // login, register, forgot
				loginType: 'password', // password, sms
				showPassword: false,
				showConfirmPassword: false,
				agreedToTerms: false,
				isLoading: false,
				smsCountdown: 0,
				smsTimer: null,
				
				// Login forms
				loginForm: {
					phone: '17856179093',
					password: 'wudong123'
				},
				smsForm: {
					phone: '',
					code: ''
				},
				registerForm: {
					phone: '',
					password: '',
					shortCode: '',
					pidInviteCode: ''
				},
				forgotForm: {
					phone: '',
					newPassword: '',
					confirmPassword: '',
					shortCode: ''
				}
			};
		},
		computed: {
			canSubmit() {
				if (!this.agreedToTerms) return false;
				
				if (this.currentMode === 'login') {
					if (this.loginType === 'password') {
						return this.loginForm.phone && this.loginForm.password;
					} else {
						return this.smsForm.phone && this.smsForm.code;
					}
				} else if (this.currentMode === 'register') {
					return this.registerForm.phone && this.registerForm.password && this.registerForm.shortCode;
				} else if (this.currentMode === 'forgot') {
					return this.forgotForm.phone && this.forgotForm.newPassword && 
						   this.forgotForm.confirmPassword && this.forgotForm.shortCode;
				}
				return false;
			}
		},
		onLoad(options) {
			// 获取邀请码
			if (options.inviteCode) {
				this.registerForm.pidInviteCode = options.inviteCode;
			}
		},
		methods: {
			...mapMutations(['updateUserItem']),

			goBack() {
				uni.navigateBack();
			},

			// 检查当前平台
			getCurrentPlatform() {
				// #ifdef APP-PLUS
				return 'app-plus';
				// #endif
				// #ifdef MP-WEIXIN
				return 'mp-weixin';
				// #endif
				// #ifdef H5
				return 'h5';
				// #endif
				return 'unknown';
			},

			// 获取系统信息用于调试
			async getSystemInfo() {
				try {
					const systemInfo = uni.getSystemInfoSync();
					console.log('系统信息:', systemInfo);
					return systemInfo;
				} catch (error) {
					console.error('获取系统信息失败:', error);
					return null;
				}
			},

			// 测试微信登录配置
			async testWechatConfig() {
				console.log('=== APP微信登录配置测试 ===');
				console.log('当前平台:', this.getCurrentPlatform());

				try {
					// 检查系统信息
					const systemInfo = uni.getSystemInfoSync();
					console.log('系统信息:', {
						platform: systemInfo.platform,
						system: systemInfo.system,
						version: systemInfo.version,
						appName: systemInfo.appName,
						appVersion: systemInfo.appVersion
					});

					// 检查OAuth提供商
					const providers = await uni.getProvider({ service: 'oauth' });
					console.log('OAuth提供商:', providers);

					// #ifdef APP-PLUS
					// 检查微信是否安装
					try {
						const isWechatInstalled = await new Promise((resolve) => {
							plus.runtime.isApplicationExist({
								pname: 'com.tencent.mm', // Android
								action: 'weixin://' // iOS
							}, (result) => {
								resolve(result);
							});
						});
						console.log('微信安装状态:', isWechatInstalled);
					} catch (error) {
						console.log('检查微信安装状态失败:', error);
					}

					// 检查APP配置
					console.log('APP配置检查完成');
					// #endif

					this.showToast('配置检查完成，请查看控制台', 'success');
					return true;
				} catch (error) {
					console.error('配置测试失败:', error);
					this.showToast('配置测试失败: ' + error.message);
					return false;
				}
			},

			getWelcomeText() {
				switch (this.currentMode) {
					case 'login': return '欢迎回来，请登录您的账号';
					case 'register': return '创建新账号，开始您的服务之旅';
					case 'forgot': return '重置密码，找回您的账号';
					default: return '';
				}
			},

			getButtonText() {
				switch (this.currentMode) {
					case 'login': return '登录';
					case 'register': return '注册';
					case 'forgot': return '重置密码';
					default: return '';
				}
			},

			switchMode(mode) {
				this.currentMode = mode;
				this.clearForms();
			},

			switchLoginType(type) {
				this.loginType = type;
			},

			togglePassword() {
				this.showPassword = !this.showPassword;
			},

			toggleConfirmPassword() {
				this.showConfirmPassword = !this.showConfirmPassword;
			},

			toggleAgreement() {
				this.agreedToTerms = !this.agreedToTerms;
			},

			clearForms() {
				this.loginForm = { phone: '', password: '' };
				this.smsForm = { phone: '', code: '' };
				this.registerForm = { phone: '', password: '', shortCode: '', pidInviteCode: '' };
				this.forgotForm = { phone: '', newPassword: '', confirmPassword: '', shortCode: '' };
			},

			navigateToAgreement(type) {
				let url = '../user/configuser';
				if (type === 'service') {
					url += '?type=service';
				} else if (type === 'privacy') {
					url += '?type=privacy';
				}
				uni.navigateTo({ url });
			},



			// 验证手机号
			validatePhone(phone) {
				const phoneReg = /^1[3-9]\d{9}$/;
				return phoneReg.test(phone);
			},

			// 发送短信验证码
			async sendSmsCode() {
				if (this.smsCountdown > 0) return;

				let phone = '';
				if (this.currentMode === 'login' && this.loginType === 'sms') {
					phone = this.smsForm.phone;
				} else if (this.currentMode === 'register') {
					phone = this.registerForm.phone;
				} else if (this.currentMode === 'forgot') {
					phone = this.forgotForm.phone;
				}

				if (!this.validatePhone(phone)) {
					return this.showToast('请输入正确的手机号');
				}

				try {
					// 调用发送验证码接口
					const response = await this.$api.base.sendSmsCode({ phone });

					if (response.code === '200') {
						this.showToast('验证码发送成功', 'success');
						this.startCountdown();
					} else {
						this.showToast(response.msg || '验证码发送失败，请重试');
					}
				} catch (error) {
					console.error('发送验证码失败:', error);
					this.showToast('验证码发送失败，请重试');
				}
			},

			// 开始倒计时
			startCountdown() {
				this.smsCountdown = 60;
				this.smsTimer = setInterval(() => {
					this.smsCountdown--;
					if (this.smsCountdown <= 0) {
						clearInterval(this.smsTimer);
						this.smsTimer = null;
					}
				}, 1000);
			},

			// 主要提交处理
			async handleSubmit() {
				if (!this.canSubmit || this.isLoading) return;

				this.isLoading = true;

				try {
					if (this.currentMode === 'login') {
						if (this.loginType === 'password') {
							await this.handlePasswordLogin();
						} else {
							await this.handleSmsLogin();
						}
					} else if (this.currentMode === 'register') {
						await this.handleRegister();
					} else if (this.currentMode === 'forgot') {
						await this.handleForgotPassword();
					}
				} catch (error) {
					console.error('操作失败:', error);
					this.showToast(error.message || '操作失败，请重试');
				} finally {
					this.isLoading = false;
				}
			},

			// 账号密码登录
			async handlePasswordLogin() {
				const { phone, password } = this.loginForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!password) {
					throw new Error('请输入密码');
				}

				const params = {
					phone,
					password: md5(password),
					platform: 2, // 用户端
					registrationId: '' // 极光推送id，暂时为空
				};

				// 使用API方法
				const response = await this.$api.base.appLoginByPass(params);
				await this.handleLoginSuccess(response);
			},

			// 短信验证码登录
			async handleSmsLogin() {
				const { phone, code } = this.smsForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!code) {
					throw new Error('请输入验证码');
				}

				const params = {
					phone,
					code,
					platform: 2, // 用户端
					registrationId: '' // 极光推送id，暂时为空
				};

				// 使用API方法
				const response = await this.$api.base.appLoginByCode(params);
				await this.handleLoginSuccess(response);
			},

			// 注册
			async handleRegister() {
				const { phone, password, shortCode, pidInviteCode } = this.registerForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!password) {
					throw new Error('请输入密码');
				}

				if (password.length < 6) {
					throw new Error('密码长度不能少于6位');
				}

				if (!shortCode) {
					throw new Error('请输入验证码');
				}

				const params = {
					phone,
					password: md5(password),
					shortCode,
					pidInviteCode: pidInviteCode || ''
				};

				// 使用API方法
				const response = await this.$api.base.appRegister(params);

				if (response.code === '200') {
					this.showToast('注册成功', 'success');
				} else {
					throw new Error(response.msg || '注册失败');
				}

				// 注册成功后自动登录
				setTimeout(() => {
					this.loginForm.phone = phone;
					this.loginForm.password = password;
					this.currentMode = 'login';
					this.loginType = 'password';
				}, 1500);
			},

			// 忘记密码
			async handleForgotPassword() {
				const { phone, newPassword, confirmPassword, shortCode } = this.forgotForm;

				if (!this.validatePhone(phone)) {
					throw new Error('请输入正确的手机号');
				}

				if (!newPassword) {
					throw new Error('请输入新密码');
				}

				if (newPassword.length < 6) {
					throw new Error('密码长度不能少于6位');
				}

				if (newPassword !== confirmPassword) {
					throw new Error('两次输入的密码不一致');
				}

				if (!shortCode) {
					throw new Error('请输入验证码');
				}

				const params = {
					phone,
					newPassword: md5(newPassword),
					confirmPassword: md5(confirmPassword),
					shortCode
				};

				// 使用API方法
				const response = await this.$api.base.appForgetPwd(params);

				if (response.code === '200') {
					this.showToast('密码重置成功', 'success');
				} else {
					throw new Error(response.msg || '密码重置失败');
				}

				// 重置成功后跳转到登录
				setTimeout(() => {
					this.loginForm.phone = phone;
					this.loginForm.password = '';
					this.currentMode = 'login';
					this.loginType = 'password';
				}, 1500);
			},

			// 处理登录成功
			async handleLoginSuccess(response) {
				console.log('登录响应:', response);

				if (!response || response.code !== '200') {
					throw new Error(response?.msg || '登录失败');
				}

				// 从响应头中提取token
				const token = extractTokenFromHeaders(response.header);

				if (!token) {
					console.error('未找到token，响应头:', response.header);
					throw new Error('登录失败，未获取到token');
				}

				// 保存token和用户信息
				uni.setStorageSync('token', token);
				this.updateUserItem({
					key: 'autograph',
					val: token
				});

				// 保存用户信息
				const userInfo = response.data;
				const userInfoFormatted = {
					phone: userInfo.phone || '',
					avatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',
					nickName: userInfo.nickName || '用户',
					userId: userInfo.id || '',
					createTime: userInfo.createTime || '',
					pid: userInfo.pid || '',
					inviteCode: userInfo.inviteCode || ''
				};

				this.updateUserItem({
					key: 'userInfo',
					val: userInfoFormatted
				});

				// 保存到本地存储
				this.saveUserInfoToStorage(userInfoFormatted);

				this.showToast('登录成功', 'success');

				// 跳转回上一页或首页
				setTimeout(() => {
					uni.navigateBack({
						fail: () => {
							uni.switchTab({
								url: '/pages/mine'
							});
						}
					});
				}, 1500);
			},

			// 保存用户信息到本地存储
			saveUserInfoToStorage(userInfo) {
				uni.setStorageSync('phone', userInfo.phone);
				uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
				uni.setStorageSync('nickName', userInfo.nickName);
				uni.setStorageSync('userId', userInfo.userId);
				uni.setStorageSync('pid', userInfo.pid);
			},

			// 微信登录
			async handleWechatLogin() {
				if (this.isLoading) return;

				console.log('=== 开始APP微信登录流程 ===');
				this.isLoading = true;
				uni.showLoading({ title: '正在启动微信...' });

				try {
					// 第一步：检查微信登录环境
					console.log('步骤1: 检查登录环境');
					await this.checkWechatEnvironment();

					// 第二步：获取微信授权码
					console.log('步骤2: 获取微信授权码');
					uni.showLoading({ title: '正在获取授权...' });
					const code = await this.getWechatCode();
					console.log('获取到授权码:', code);

					// 第三步：获取用户信息（可选）
					console.log('步骤3: 获取用户信息');
					uni.showLoading({ title: '获取用户信息...' });
					const userInfo = await this.getWechatUserInfo();

					// 第四步：调用后端登录接口
					console.log('步骤4: 调用登录接口');
					uni.showLoading({ title: '登录中...' });
					await this.callWechatLoginAPI(code, userInfo);

					console.log('=== 微信登录流程完成 ===');

				} catch (error) {
					console.error('微信登录失败:', error);

					// 根据错误类型提供不同的提示
					let errorMessage = error.message || '微信登录失败';
					if (errorMessage.includes('取消')) {
						// 用户主动取消，不显示错误提示
						console.log('用户取消微信登录');
					} else {
						this.showToast(errorMessage);
					}
				} finally {
					this.isLoading = false;
					uni.hideLoading();
				}
			},

			// 检查微信登录环境
			async checkWechatEnvironment() {
				console.log('检查微信登录环境...');

				// 检查当前平台
				const platform = this.getCurrentPlatform();
				console.log('当前平台:', platform);

				// 只在APP环境下支持微信登录
				if (platform !== 'app-plus') {
					if (platform === 'h5') {
						throw new Error('H5环境不支持微信登录，请下载APP使用');
					} else if (platform === 'mp-weixin') {
						throw new Error('请使用小程序原生登录方式');
					} else {
						throw new Error('当前环境不支持微信登录，请在APP中使用');
					}
				}

				// 检查设备是否安装微信
				// #ifdef APP-PLUS
				try {
					const isWechatInstalled = await new Promise((resolve) => {
						plus.runtime.isApplicationExist({
							pname: 'com.tencent.mm', // Android微信包名
							action: 'weixin://' // iOS微信scheme
						}, (result) => {
							resolve(result);
						});
					});

					if (!isWechatInstalled) {
						throw new Error('设备未安装微信，请先安装微信客户端');
					}
				} catch (error) {
					console.warn('检查微信安装状态失败:', error);
					// 不阻止登录流程，继续尝试
				}
				// #endif

				// 检查OAuth服务提供商
				try {
					const providers = await uni.getProvider({ service: 'oauth' });
					console.log('OAuth提供商:', providers);

					if (!providers.provider || !providers.provider.includes('weixin')) {
						throw new Error('微信登录服务未配置，请联系开发者');
					}
				} catch (error) {
					console.error('获取OAuth服务失败:', error);
					throw new Error('获取登录服务失败，请检查网络连接或重启APP');
				}
			},

			// 获取微信授权码
			async getWechatCode() {
				console.log('获取微信授权码...');

				return new Promise((resolve, reject) => {
					// 设置超时
					const timeout = setTimeout(() => {
						reject(new Error('微信授权超时，请重试'));
					}, 30000); // 30秒超时

					uni.login({
						provider: 'weixin',
						success: (res) => {
							clearTimeout(timeout);
							console.log('获取微信code成功:', res);

							if (res.code) {
								resolve(res.code);
							} else {
								reject(new Error('微信返回的授权码为空'));
							}
						},
						fail: (err) => {
							clearTimeout(timeout);
							console.error('获取微信code失败:', err);

							// APP环境下的错误处理
							let errorMsg = '微信授权失败';

							// 根据错误码处理
							if (err.code) {
								switch (err.code) {
									case 1000:
										errorMsg = '用户取消了微信授权';
										break;
									case 1001:
										errorMsg = '微信授权被拒绝';
										break;
									case 1002:
										errorMsg = '网络错误，请检查网络连接';
										break;
									case 1003:
										errorMsg = '用户点击了拒绝按钮';
										break;
									case 1004:
										errorMsg = '应用未安装微信';
										break;
									case 1005:
										errorMsg = '微信版本过低，请更新微信';
										break;
									default:
										errorMsg = `微信授权失败 (错误码: ${err.code})`;
								}
							} else if (err.errMsg) {
								errorMsg = err.errMsg;
							} else if (err.message) {
								errorMsg = err.message;
							}

							reject(new Error(errorMsg));
						}
					});
				});
			},

			// 获取微信用户信息
			async getWechatUserInfo() {
				console.log('获取微信用户信息...');

				// 在APP环境下，用户信息获取是可选的
				// 很多情况下只需要code就可以完成登录
				return new Promise((resolve) => {
					// 设置较短的超时时间，因为这不是必需的
					const timeout = setTimeout(() => {
						console.log('获取用户信息超时，使用空数据继续');
						resolve({
							encryptedData: '',
							iv: ''
						});
					}, 10000); // 10秒超时

					uni.getUserInfo({
						provider: 'weixin',
						success: (res) => {
							clearTimeout(timeout);
							console.log('获取用户信息成功:', res);
							resolve({
								encryptedData: res.encryptedData || '',
								iv: res.iv || ''
							});
						},
						fail: (err) => {
							clearTimeout(timeout);
							console.warn('获取用户信息失败，继续登录流程:', err);
							// 在APP环境下，用户信息获取失败是常见的，不影响登录
							resolve({
								encryptedData: '',
								iv: ''
							});
						}
					});
				});
			},

			// 调用后端微信登录接口
			async callWechatLoginAPI(code, userInfo) {
				console.log('调用后端微信登录接口...');

				const params = {
					code: code,
					encryptedData: userInfo.encryptedData,
					iv: userInfo.iv,
					pid: this.getInviteCode() // 获取邀请码
				};

				console.log('登录参数:', { ...params, encryptedData: '***', iv: '***' });

				try {
					const response = await this.$api.base.appLoginByWechat(params);
					console.log('登录接口响应:', response);

					if (response && response.code === '200') {
						await this.handleLoginSuccess(response);
						this.showToast('微信登录成功', 'success');
					} else {
						throw new Error(response?.msg || '登录失败，请重试');
					}
				} catch (error) {
					console.error('调用登录接口失败:', error);
					throw new Error('网络请求失败，请检查网络连接');
				}
			},

			// 获取邀请码
			getInviteCode() {
				// 可以从页面参数、存储等地方获取邀请码
				return this.registerForm.pidInviteCode || '';
			},

			// 简化版微信登录（用于调试）
			async simpleWechatLogin() {
				if (this.isLoading) return;

				this.isLoading = true;
				console.log('=== 简化版微信登录开始 ===');

				try {
					uni.showLoading({ title: '微信登录中...' });

					// 直接调用微信登录
					const loginResult = await new Promise((resolve, reject) => {
						uni.login({
							provider: 'weixin',
							onlyAuthorize:true,
							success: resolve,
							fail: reject
						});
					});

					console.log('微信登录结果:', loginResult);

					if (!loginResult.code) {
						throw new Error('未获取到微信授权码');
					}

					// 调用后端接口
					const response = await this.$api.base.appLoginByWechat({
						code: loginResult.code,
						encryptedData: '',
						iv: '',
						pid: ''
					});

					console.log('后端响应:', response);

					if (response && response.code === '200') {
						await this.handleLoginSuccess(response);
						this.showToast('登录成功', 'success');
					} else {
						throw new Error(response?.msg || '登录失败');
					}

				} catch (error) {
					console.error('简化版微信登录失败:', error);
					this.showToast(error.message || '登录失败');
				} finally {
					this.isLoading = false;
					uni.hideLoading();
				}
			},

			// 显示提示信息
			showToast(title, icon = 'none') {
				uni.showToast({
					title,
					icon,
					duration: 2000
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #2E80FE 0%, #1A6BD8 100%);
		padding: 0 40rpx;
		position: relative;

		// 添加背景装饰
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
			pointer-events: none;
		}
	}

	.header {
		display: flex;
		align-items: center;
		padding: 80rpx 0 60rpx;
		position: relative;
		z-index: 1;

		.back-btn {
			position: absolute;
			left: 0;
			width: 72rpx;
			height: 72rpx;
			background: rgba(255, 255, 255, 0.15);
			backdrop-filter: blur(10rpx);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&:active {
				background: rgba(255, 255, 255, 0.25);
				transform: scale(0.95);
			}
		}

		.title {
			flex: 1;
			text-align: center;
			font-size: 40rpx;
			font-weight: 600;
			color: #fff;
			letter-spacing: 2rpx;
		}
	}

	.logo-section {
		text-align: center;
		margin-bottom: 100rpx;
		z-index: 1;
		position: relative;

		.logo {
			width: 140rpx;
			height: 140rpx;
			border-radius: 32rpx;
			margin-bottom: 32rpx;
			box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
			border: 4rpx solid rgba(255, 255, 255, 0.2);
		}

		.app-name {
			font-size: 56rpx;
			font-weight: 700;
			color: #fff;
			margin-bottom: 20rpx;
			letter-spacing: 4rpx;
			text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
		}

		.welcome-text {
			font-size: 30rpx;
			color: rgba(255, 255, 255, 0.9);
			line-height: 1.5;
			font-weight: 300;
		}
	}

	.form-container {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20rpx);
		border-radius: 32rpx;
		padding: 80rpx 50rpx;
		margin-bottom: 50rpx;
		box-shadow: 0 32rpx 80rpx rgba(0, 0, 0, 0.15);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		position: relative;
		z-index: 1;
	}

	.tab-switcher {
		display: flex;
		background: #f8f9fa;
		border-radius: 16rpx;
		margin-bottom: 80rpx;
		padding: 6rpx;
		position: relative;

		.tab-item {
			flex: 1;
			text-align: center;
			padding: 24rpx 20rpx;
			border-radius: 12rpx;
			font-size: 30rpx;
			color: #666;
			transition: all 0.3s ease;
			position: relative;
			z-index: 1;

			&.active {
				background: #fff;
				color: #2E80FE;
				font-weight: 600;
				box-shadow: 0 8rpx 24rpx rgba(46, 128, 254, 0.15);
				transform: translateY(-2rpx);
			}
		}
	}

	.input-group {
		.input-item {
			display: flex;
			align-items: center;
			background: #f8f9fa;
			border-radius: 20rpx;
			padding: 0 36rpx;
			margin-bottom: 36rpx;
			height: 112rpx;
			border: 2rpx solid transparent;
			transition: all 0.3s ease;

			&:focus-within {
				background: #fff;
				border-color: #2E80FE;
				box-shadow: 0 0 0 8rpx rgba(46, 128, 254, 0.1);
			}

			.input-field {
				flex: 1;
				margin-left: 24rpx;
				font-size: 32rpx;
				color: #333;
				font-weight: 400;
			}

			.eye-icon, .sms-btn {
				margin-left: 24rpx;
			}

			.eye-icon {
				padding: 8rpx;
				border-radius: 50%;
				transition: background 0.2s ease;

				&:active {
					background: rgba(46, 128, 254, 0.1);
				}
			}

			.sms-btn {
				background: linear-gradient(135deg, #2E80FE, #1A6BD8);
				color: #fff;
				padding: 20rpx 32rpx;
				border-radius: 12rpx;
				font-size: 26rpx;
				font-weight: 500;
				transition: all 0.3s ease;

				&.disabled {
					background: #e0e0e0;
					color: #999;
				}

				&:not(.disabled):active {
					transform: scale(0.95);
				}
			}
		}
	}

	.forgot-password {
		text-align: right;
		color: #2E80FE;
		font-size: 28rpx;
		margin-top: 24rpx;
		font-weight: 500;
		transition: color 0.3s ease;

		&:active {
			color: #1A6BD8;
		}
	}

	.agreement-section {
		margin-bottom: 80rpx;
		z-index: 1;
		position: relative;

		.checkbox-container {
			display: flex;
			align-items: flex-start;

			.checkbox {
				width: 40rpx;
				height: 40rpx;
				border: 2rpx solid rgba(255, 255, 255, 0.6);
				border-radius: 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 24rpx;
				margin-top: 4rpx;
				flex-shrink: 0;
				transition: all 0.3s ease;

				&.checked {
					background: rgba(255, 255, 255, 0.9);
					border-color: rgba(255, 255, 255, 0.9);
					transform: scale(1.1);
				}
			}

			.agreement-text {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.9);
				line-height: 1.6;
				font-weight: 300;

				.link {
					color: #fff;
					text-decoration: underline;
					font-weight: 400;
					transition: opacity 0.3s ease;

					&:active {
						opacity: 0.8;
					}
				}
			}
		}
	}

	.wechat-login-button {
		width: 100%;
		height: 112rpx;
		background: linear-gradient(135deg, #07C160, #05A050);
		border-radius: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 40rpx;
		box-shadow: 0 16rpx 48rpx rgba(7, 193, 96, 0.3);
		border: 2rpx solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
		position: relative;
		z-index: 1;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 8rpx 24rpx rgba(7, 193, 96, 0.4);
		}

		.wechat-icon {
			width: 52rpx;
			height: 52rpx;
			margin-right: 24rpx;
		}
	}

	.debug-button {
		width: 100%;
		height: 80rpx;
		background: #ff9500;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 28rpx;
		margin-bottom: 20rpx;

		&.simple {
			background: #007aff;
		}
	}

	.action-button {
		width: 100%;
		height: 112rpx;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.8));
		border-radius: 56rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #2E80FE;
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 50rpx;
		box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.15);
		border: 2rpx solid rgba(255, 255, 255, 0.3);
		transition: all 0.3s ease;
		position: relative;
		z-index: 1;

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
		}

		&.disabled {
			background: rgba(255, 255, 255, 0.3);
			color: rgba(255, 255, 255, 0.6);
			box-shadow: none;
			transform: none;
		}
	}

	.switch-links {
		text-align: center;
		padding-bottom: 60rpx;
		position: relative;
		z-index: 1;

		.link-text {
			color: rgba(255, 255, 255, 0.9);
			font-size: 30rpx;
			font-weight: 400;
			padding: 20rpx;
			border-radius: 12rpx;
			transition: all 0.3s ease;

			&:active {
				background: rgba(255, 255, 255, 0.1);
				color: #fff;
			}
		}
	}
</style>
